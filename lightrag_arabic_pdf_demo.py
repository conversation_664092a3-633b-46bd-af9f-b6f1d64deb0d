import os
import sys
sys.path.insert(0, '.')  # Add the current directory to the Python path
sys.path.insert(0, './LightRAG')  # Add the LightRAG directory to the Python path
import numpy as np
import google.generativeai as genai
from dotenv import load_dotenv
from lightrag import LightRAG, QueryParam
from lightrag.utils import EmbeddingFunc
from sentence_transformers import SentenceTransformer
from lightrag.kg.shared_storage import initialize_pipeline_status
import traceback
import re
from typing import List
import json
import asyncio
import nest_asyncio
from PyPDF2 import PdfReader
# Apply nest_asyncio to solve event loop issues
nest_asyncio.apply()

# Load environment variables
load_dotenv()
gemini_api_key = os.getenv("GEMINI_API_KEY")
if not gemini_api_key:
    # Fallback to a default key if not provided
    gemini_api_key = "AIzaSyCPP-8M6IBIAcogqfzItsv6Sc21pmC6Pzg"


try:
    genai.configure(api_key=gemini_api_key)
    print("Configured Gemini API successfully")
except Exception as e:
    print(f"Error configuring Gemini API: {str(e)}")
   

# Create a working directory for our RAG system
WORKING_DIR = "./arabic_lightrag_demo_gpt"
os.makedirs(WORKING_DIR, exist_ok=True)

# Function to extract text from PDF files
def extract_text_from_pdf(pdf_path: str) -> str:
    try:
        # Read the PDF files
        reader = PdfReader(pdf_path)
        text = ""

        # Get PDF metadata
        if reader.metadata:
            title = reader.metadata.get('/Title', '')
            if title and title != 'Unknown':
                text += f"Document Title: {title}\n"


        # Extract text from each page
        for page_num, page in enumerate(reader.pages):
            page_text = page.extract_text()
            if page_text and page_text.strip():
                # Clean up the text
                cleaned_text = clean_arabic_text(page_text)
                if cleaned_text:
                    text += f"Page {page_num + 1}:\n{cleaned_text}\n\n"

        return text
    except Exception as e:
        print(f"Error extracting text from PDF {pdf_path}: {str(e)}")
        traceback.print_exc()
        return ""

def clean_arabic_text(text):
    if not text:
        return ""
    import re
    # Replace multiple spaces with single space
    text = re.sub(r'\s+', ' ', text)
    # Remove page headers/footers
    lines = text.split('\n')
    cleaned_lines = []
    for line in lines:
        line = line.strip()
        if len(line) < 3:  # Skip very short lines
            continue
        # Skip lines that are mostly numbers or special characters
        arabic_chars = sum(1 for char in line if '\u0600' <= char <= '\u06FF')
        latin_chars = sum(1 for char in line if char.isalpha() and not ('\u0600' <= char <= '\u06FF'))
        total_chars = len(line.replace(' ', ''))

        if total_chars > 0:
            # Keep lines with significant Arabic content
            if arabic_chars / total_chars > 0.2 or arabic_chars > 5:
                cleaned_lines.append(line)
            # Keep lines with mixed content that are long enough
            elif len(line) > 15 and (arabic_chars > 3 or latin_chars > 5):
                cleaned_lines.append(line)
            # Keep structural elements
            elif any(keyword in line.lower() for keyword in ['page', 'صفحة', 'chapter', 'فصل', 'section', 'قسم']):
                cleaned_lines.append(line)

    return '\n'.join(cleaned_lines)

# Function to split text into chunks
def split_text_into_chunks(text: str, max_length: int = 1500, overlap: int = 200) -> List[str]:
    paragraphs = re.split(r'\n\s*\n', text)
    chunks = []
    current_chunk = ""
    for paragraph in paragraphs:
        if len(current_chunk) + len(paragraph) > max_length and current_chunk:
            chunks.append(current_chunk)
            # Start new chunk with overlap from the end of the previous chunk
            if len(current_chunk) > overlap:
                current_chunk = current_chunk[-overlap:] + "\n\n" + paragraph
            else:
                current_chunk = paragraph
        else:
            if current_chunk:
                current_chunk += "\n\n" + paragraph
            else:
                current_chunk = paragraph
    if current_chunk:
        chunks.append(current_chunk)
    return chunks

# LLM function using Gemini API with retry logic
async def llm_model_func(prompt, system_prompt=None, history_messages=None, **_) -> str:
    import time
    max_retries = 3
    base_delay = 1

    for attempt in range(max_retries):
        try:
            model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')
            # Combine prompts sys prompt, history, and user prompt
            if history_messages is None:
                history_messages = []
            combined_prompt = ""
            if system_prompt:
                combined_prompt += f"{system_prompt}\n"
            for msg in history_messages:
                if isinstance(msg, dict) and 'role' in msg and 'content' in msg:
                    combined_prompt += f"{msg['role']}: {msg['content']}\n"
            # Add the user prompt
            combined_prompt += f"user: {prompt}"
            # Call
            response = model.generate_content(combined_prompt)
            return response.text
        except Exception as e:
            if "500" in str(e) and attempt < max_retries - 1:
                delay = base_delay * (2 ** attempt)  # Exponential backoff
                print(f"Gemini API error (attempt {attempt + 1}/{max_retries}): {str(e)}")
                print(f"Retrying in {delay} seconds")
                time.sleep(delay)
                continue
            else:
                print(f"Error calling Gemini API: {str(e)}")
                return f"Error generating response: {str(e)}"

# Global embedding model instance to avoid reloading
_embedding_model = None

def get_embedding_model():
    global _embedding_model
    if _embedding_model is None:
        _embedding_model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
        print("Embedding model loaded")
    return _embedding_model

# Embedding
async def multilingual_embedding_func(texts: list[str]) -> np.ndarray:
    try:
        model = get_embedding_model()
        embeddings = model.encode(texts, convert_to_numpy=True)
        return embeddings
    except Exception as e:
        print(f"Error generating embeddings: {str(e)}")
        # Return zero embeddings as fallback
        return np.zeros((len(texts), 384))  # 384 is the dimension of the model

async def initialize_rag():
    try:
        print("Creating LightRAG instance")
        rag = LightRAG(
            working_dir=WORKING_DIR,
            llm_model_func=llm_model_func,
            embedding_func=EmbeddingFunc(
                embedding_dim=384,  # Dimension for the model
                max_token_size=8192,
                func=multilingual_embedding_func,
            ),

            addon_params={"language": "Arabic"},
            chunk_token_size=1000,
            chunk_overlap_token_size=100
        )

        print("Initializing storages")
        await rag.initialize_storages()
        print("Initializing pipeline status")
        await initialize_pipeline_status()
        print("RAG initialization complete")
        return rag
    except Exception as e:
        print(f"Error initializing LightRAG: {str(e)}")
        traceback.print_exc()
        raise

async def process_document(rag, file_path, is_pdf=False):
    try:
        # Read the document based on its type
        if is_pdf:
            print(f" Extracting text from PDF")
            text = extract_text_from_pdf(file_path)
            if not text:
                print(f" Failed to extract text from PDF: {file_path}")
                return False
            print(f" Successfully extracted text from PDF")
            print(f"Content length: {len(text):,} characters")
        else:
            print(f" Reading text file")
            # Read text file with UTF-8 encoding to support Arabic
            with open(file_path, "r", encoding="utf-8") as file:
                text = file.read()

            print(f"Successfully read text file")
            print(f"Content length: {len(text):,} characters")


        # Split text into chunks to handle large documents
        print(f" Splitting document into chunks")
        chunks = split_text_into_chunks(text, max_length=3000, overlap=300)  # Larger chunks = fewer API calls
        print(f"   Created {len(chunks)} chunks")

        # Insert each chunk into the RAG system
        print(f"Inserting chunks")
        for i, chunk in enumerate(chunks):
            if i % 5 == 0 or i == len(chunks) - 1:  # Show progress every 5 chunks
                print(f" Inserting chunk {i+1}/{len(chunks)} ({((i+1)/len(chunks)*100):.1f}%)")
            await rag.ainsert(chunk)

        print(f"Document processed")
        return True
    except Exception as e:
        print(f" Error processing document {file_path}: {str(e)}")
        traceback.print_exc()
        return False

async def amain():
    print("Initializing LightRAG")
    rag = await initialize_rag()
    print("LightRAG initialized successfully")

    test_text = ["هذا نص تجريبي"]
    embedding = await multilingual_embedding_func(test_text)
    embedding_dim = embedding.shape[1]
    print("\n=======================")
    print("Test embedding function")
    print("========================")
    print(f"Test text: {test_text}")
    print(f"Detected embedding dimension: {embedding_dim}\n\n")

    file_paths = []

    # Process only PDF files from hr folder
    pdf_dir = "hr"
    if os.path.exists(pdf_dir):
        pdf_files = [f for f in os.listdir(pdf_dir) if f.lower().endswith('.pdf')]
        print(f"Found {len(pdf_files)} PDF files in hr folder: {pdf_files}")

        for file in sorted(pdf_files):
            file_paths.append({
                "path": os.path.join(pdf_dir, file),
                "is_pdf": True
            })
    else:
        print(f" hr folder not found")

    # Process each PDF file
    if not file_paths:
        print(" No PDF files found")
    else:
        print(f"\n Starting to process {len(file_paths)} PDF files")
        all_success = True
        processed_count = 0

        for i, file_info in enumerate(file_paths, 1):
            file_path = file_info["path"]
            is_pdf = file_info["is_pdf"]

            print(f"\n{'='*60}")
            print(f" Processing PDF {i}/{len(file_paths)}: {os.path.basename(file_path)}")
            print(f" Full path: {file_path}")
            print(f"{'='*60}")

            try:
                success = await process_document(rag, file_path, is_pdf)
                if success:
                    processed_count += 1
                    print(f"Successfully processed {os.path.basename(file_path)}")
                else:
                    print(f"Failed to process {os.path.basename(file_path)}")
                    all_success = False
            except Exception as e:
                print(f" Error processing {os.path.basename(file_path)}: {str(e)}")
                all_success = False

        print(f"\n{'='*60}")
        print(f" PROCESSING SUMMARY")
        print(f"{'='*60}")
        print(f"Total PDF files: {len(file_paths)}")
        print(f"Successfully processed: {processed_count}")
        print(f"Failed: {len(file_paths) - processed_count}")

        if not all_success:
            print(" Some files not  processed")
        else:
            print("All PDF files processed")


    # Define queries
    arabic_queries = [
        "ما هو العامل المنزلي؟",
        "ما الشروط التي يجب توافرها عند نقل خدمات العامل المنزلي إلى صاحب عمل جديد؟"
    ]

    # Process predefined queries
    for query in arabic_queries:
        print(f"\n\nQuery: {query}")

        try:
            print("using RAG system")
            param = QueryParam(
                mode="hybrid",  # Use hybrid search
                stream=False,
                top_k=5,
                response_type="multiple paragraphs"
            )
            response = await rag.aquery(query, param=param)
            print(f"Response (RAG): {response}")
        except Exception as e:
            print(f"Error in RAG query: {str(e)}")
            # Fall back to direct LLM call
            try:
                print("Falling back to direct LLM call")
                response = await llm_model_func(f"أجب على هذا السؤال باللغة العربية: {query}")
                print(f"Response (LLM fallback): {response}")
            except Exception as e2:
                print(f"Error in LLM fallback: {str(e2)}")
                print(f"Error: {str(e2)}")

    # Interactive mode - allow user to ask questions
    print("\n\n=== Interactive Mode ===")
    print("Type 'exit' to quit")

    while True:
        user_query = input("\nEnter your question in Arabic: ")
        if user_query.lower() == 'exit':
            break

        try:
            # Try using  RAG
            print("hybrid RAG")
            param = QueryParam(
                mode="hybrid",  # Use hybrid search for better results
                stream=False,
                top_k=5,
                response_type="multiple paragraphs"
            )
            response = await rag.aquery(user_query, param=param)
            print(f"\nResponse (RAG): {response}")
        except Exception as e:
            print(f"Error in RAG query: {str(e)}")
            # Fall back to direct LLM call
            try:
                print("Falling back to direct LLM call...")
                response = await llm_model_func(f"أجب على هذا السؤال باللغة العربية: {user_query}")
                print(f"\nResponse (LLM fallback): {response}")
            except Exception as e2:
                print(f"Error in LLM fallback: {str(e2)}")
                print(f"Error: {str(e2)}")

def main():
    asyncio.run(amain())

if __name__ == "__main__":
    main()