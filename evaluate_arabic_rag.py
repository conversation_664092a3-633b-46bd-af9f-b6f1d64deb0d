import os
import sys
import json
import argparse
from typing import List, Dict, Any, Optional, Tuple
import asyncio
from openai import OpenAI
import backoff
import nest_asyncio
import numpy as np
from dotenv import load_dotenv

sys.path.insert(0, '.')
sys.path.insert(0, './LightRAG')

from lightrag import LightRAG, QueryParam
from lightrag.utils import EmbeddingFunc
from sentence_transformers import SentenceTransformer
from lightrag.kg.shared_storage import initialize_pipeline_status
import traceback

nest_asyncio.apply()

load_dotenv()
openai_api_key = os.getenv("OPENAI_API_KEY")
if not openai_api_key:
    raise ValueError("OPENAI_API_KEY not found in environment variables")

client = OpenAI(api_key=openai_api_key)



WORKING_DIR ="./arabic_lightrag_demo2"


async def llm_model_func(prompt, system_prompt=None, history_messages=None, **_) -> str:
    try:
        messages = []

        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})

        if history_messages:
            for msg in history_messages:
                if isinstance(msg, dict) and 'role' in msg and 'content' in msg:
                    messages.append(msg)

        messages.append({"role": "user", "content": prompt})

        response = client.chat.completions.create(
            model="gpt-4o",
            messages=messages,
            temperature=0.3
        )
        return response.choices[0].message.content
    except Exception as e:
        print(f"Error calling OpenAI API: {str(e)}")
        return f"Error generating response: {str(e)}"

async def multilingual_embedding_func(texts: list[str]) -> np.ndarray:
    try:
        model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
        embeddings = model.encode(texts, convert_to_numpy=True)
        return embeddings
    except Exception as e:
        print(f"Error generating embeddings: {str(e)}")
        return np.zeros((len(texts), 384))

async def initialize_rag():
    try:
        rag = LightRAG(
            working_dir=WORKING_DIR,
            llm_model_func=llm_model_func,
            embedding_func=EmbeddingFunc(
                embedding_dim=384,
                max_token_size=8192,
                func=multilingual_embedding_func,
            ),
            addon_params={"language": "Arabic"},
            chunk_token_size=1000,
            chunk_overlap_token_size=100
        )

        await rag.initialize_storages()
        await initialize_pipeline_status()
        return rag
    except Exception as e:
        print(f"Error initializing LightRAG: {str(e)}")
        traceback.print_exc()
        raise

async def query_rag_system(rag, question: str, mode: str = "hybrid") -> str:
    try:
        param = QueryParam(
            mode=mode,
            stream=False,
            top_k=5,
            response_type="multiple paragraphs"
        )
        response = await rag.aquery(question, param=param)
        return response
    except Exception as e:
        print(f"Error querying RAG system: {str(e)}")
        return f"Error: {str(e)}"

async def evaluate_qa_system_async(test_data_path: str, output_path: str, use_hybrid: bool = True):
    """
    Evaluate the LightRAG system on a test dataset with ground truth answers.

    Args:
        test_data_path: Path to test data JSON file (with questions and expected answers)
        output_path: Path to save evaluation results
        use_hybrid: Whether to use hybrid search mode (True) or naive mode (False)
    """
    # Load test data
    with open(test_data_path, "r", encoding="utf-8") as f:
        test_data = json.load(f)

    # Initialize RAG system
    print("Initializing LightRAG system")
    rag = await initialize_rag()
    print("LightRAG system initialized successfully")

    # Evaluate each question
    results = []
    total_score = 0.0

    # Counters for result categories
    correct_count = 0      # Answers with average score >= 4.5
    partial_count = 0      # Answers with average score between 2.5 and 4.5
    incorrect_count = 0    # Answers with average score < 2.5

    search_mode = "hybrid" if use_hybrid else "naive"
    rag_type = "Hybrid RAG" if use_hybrid else "Naive RAG"
    print(f"Evaluating {rag_type} system on {len(test_data)} test questions")

    for i, item in enumerate(test_data):
        question = item["question"]
        expected_answer = item["expected_answer"]

        print(f"Processing question {i+1}/{len(test_data)}: {question}")

        # Get the actual answer from our system
        actual_answer = await query_rag_system(rag, question, mode=search_mode)

        # Evaluate the answer using Gemini as a judge
        evaluation = await evaluate_answer_with_gemini(question, expected_answer, actual_answer)

        # Categorize result based on average score
        result_category = ""
        if evaluation["average_score"] >= 4.5:
            result_category = "Correct"
            correct_count += 1
        elif evaluation["average_score"] >= 2.5:
            result_category = "Partially Correct"
            partial_count += 1
        else:
            result_category = "Incorrect"
            incorrect_count += 1

        result = {
            "question": question,
            "expected_answer": expected_answer,
            "actual_answer": actual_answer,
            "correctness_score": evaluation["correctness_score"],
            "completeness_score": evaluation["completeness_score"],
            "relevance_score": evaluation["relevance_score"],
            "average_score": evaluation["average_score"],
            "result_category": result_category,
            "comments": evaluation["comments"]
        }

        results.append(result)
        total_score += evaluation["average_score"]

        print(f"  Scores - Correctness: {evaluation['correctness_score']}, Completeness: {evaluation['completeness_score']}, Relevance: {evaluation['relevance_score']}")
        print(f"  Average score: {evaluation['average_score']} - Category: {result_category}")

    # Calculate overall statistics
    avg_score = total_score / len(test_data) if test_data else 0

    stats = {
        "total_questions": len(test_data),
        "correct_answers": correct_count,
        "partially_correct_answers": partial_count,
        "incorrect_answers": incorrect_count,
        "correct_percentage": round(correct_count * 100 / len(test_data), 2) if test_data else 0,
        "partially_correct_percentage": round(partial_count * 100 / len(test_data), 2) if test_data else 0,
        "incorrect_percentage": round(incorrect_count * 100 / len(test_data), 2) if test_data else 0,
        "average_score": avg_score,
        "model_used": "gpt-4o",
        "rag_approach": rag_type
    }

    # Save results
    output = {
        "stats": stats,
        "results": results
    }

    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(output, f, ensure_ascii=False, indent=2)

    # Print summary statistics
    print("\nEvaluation Summary:")
    print(f"RAG Approach: {rag_type}")
    print(f"Total Questions: {len(test_data)}")
    print(f"Correct Answers: {correct_count} ({stats['correct_percentage']}%)")
    print(f"Partially Correct Answers: {partial_count} ({stats['partially_correct_percentage']}%)")
    print(f"Incorrect Answers: {incorrect_count} ({stats['incorrect_percentage']}%)")
    print(f"Average Score: {avg_score:.2f}/5.0")
    print(f"Results saved to {output_path}")

def evaluate_qa_system(test_data_path: str, output_path: str, use_hybrid: bool = True):
    """
    Synchronous wrapper for the async evaluation function.
    """
    asyncio.run(evaluate_qa_system_async(test_data_path, output_path, use_hybrid))

async def evaluation_llm_func(prompt, eval_model: str = "gpt-4o") -> str:
    """
    LLM function specifically for evaluation using a different model.
    """
    try:
        response = client.chat.completions.create(
            model=eval_model,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1
        )
        return response.choices[0].message.content
    except Exception as e:
        print(f"Error calling OpenAI API for evaluation: {str(e)}")
        return await llm_model_func(prompt)

async def evaluate_answer_with_gemini(question: str, expected_answer: str, actual_answer: str) -> Dict[str, Any]:
    """
    Evaluate the quality of the RAG system's answer compared to the expected answer using Gemini.

    Args:
        question: The question that was asked
        expected_answer: The expected (ground truth) answer
        actual_answer: The actual answer from the RAG system

    Returns:
        Dictionary with evaluation scores and comments
    """
    # Check if the question is in Arabic
    is_arabic = any("\u0600" <= c <= "\u06FF" for c in question)

    if is_arabic:
        prompt = f"""
        تقييم إجابة على سؤال:

        السؤال: {question}

        الإجابة المتوقعة (المرجعية): {expected_answer}

        الإجابة الفعلية: {actual_answer}

        قيّم الإجابة الفعلية مقارنة بالإجابة المتوقعة على مقياس من 1 إلى 5 في الجوانب التالية:

        1. الدقة (هل المعلومات صحيحة؟): [درجة من 1 إلى 5]
        2. الاكتمال (هل تغطي جميع النقاط الرئيسية في الإجابة المتوقعة؟): [درجة من 1 إلى 5]
        3. الصلة (هل الإجابة مرتبطة بالسؤال؟): [درجة من 1 إلى 5]

        متوسط الدرجات: [المتوسط]

        تعليقات: [تعليقات قصيرة حول الإجابة]

        ملاحظة: أعطني التقييم بالتنسيق المذكور أعلاه بالضبط وبدون أي نص إضافي.
        """
    else:
        prompt = f"""
        Evaluate the answer to a question:

        Question: {question}

        Expected (reference) answer: {expected_answer}

        Actual answer: {actual_answer}

        Rate the actual answer compared to the expected answer on a scale of 1 to 5 in the following aspects:

        1. Correctness (Is the information accurate?): [score from 1 to 5]
        2. Completeness (Does it cover all key points in the expected answer?): [score from 1 to 5]
        3. Relevance (Is the answer relevant to the question?): [score from 1 to 5]

        Average score: [average]

        Comments: [brief comments about the answer]

        Note: Give me the evaluation in exactly the format mentioned above without any additional text.
        """

    try:
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1
        )
        eval_text = response.choices[0].message.content

        correctness_score = extract_score(eval_text, "الدقة" if is_arabic else "Correctness")
        completeness_score = extract_score(eval_text, "الاكتمال" if is_arabic else "Completeness")
        relevance_score = extract_score(eval_text, "الصلة" if is_arabic else "Relevance")

        # Extract or calculate average
        avg_score = extract_score(eval_text, "متوسط الدرجات" if is_arabic else "Average score")
        if avg_score is None:
            avg_score = (correctness_score + completeness_score + relevance_score) / 3 if all(x is not None for x in [correctness_score, completeness_score, relevance_score]) else None

        # Extract comments
        comments = ""
        comments_prefix = "تعليقات:" if is_arabic else "Comments:"
        for line in eval_text.split('\n'):
            if comments_prefix in line:
                comments = line.split(':', 1)[1].strip() if ':' in line else ""
                break

        return {
            "correctness_score": correctness_score or 0,
            "completeness_score": completeness_score or 0,
            "relevance_score": relevance_score or 0,
            "average_score": avg_score or 0,
            "comments": comments
        }

    except Exception as e:
        print(f"Error evaluating answer: {e}")
        return {
            "correctness_score": 0,
            "completeness_score": 0,
            "relevance_score": 0,
            "average_score": 0,
            "comments": f"Error: {str(e)}"
        }

def extract_score(text: str, category: str) -> Optional[float]:
    """
    Extract a numeric score from evaluation text.

    Args:
        text: The evaluation text
        category: The category to extract score for

    Returns:
        Extracted score as a float, or None if not found
    """
    import re

    # Try multiple patterns to handle different formats
    patterns = [
        fr'{category}.*?(\d+\.?\d*)',  # Category name followed by number
        fr'{category}.*?(\d+)',        # Category name followed by integer
        r'(\d+\.?\d*)\/5',             # Any X/5 score
        r'(\d+)\/5'                    # Any X/5 integer score
    ]

    for pattern in patterns:
        matches = re.search(pattern, text, re.IGNORECASE)
        if matches:
            try:
                return float(matches.group(1))
            except (ValueError, IndexError):
                pass

    return None



def main():
    parser = argparse.ArgumentParser(description="Evaluate Arabic LightRAG System")
    parser.add_argument("--test-data", "-t", required=True, help="Path to test data JSON file")
    parser.add_argument("--output", "-o", required=True, help="Output path for evaluation results")
    parser.add_argument("--hybrid", action="store_true", help="Use hybrid search mode (default: naive)")

    args = parser.parse_args()

    use_hybrid = getattr(args, 'hybrid', False)
    evaluate_qa_system(args.test_data, args.output, use_hybrid)

if __name__ == "__main__":
    main()
